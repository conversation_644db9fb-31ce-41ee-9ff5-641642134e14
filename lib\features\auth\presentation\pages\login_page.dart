import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/utils/helpers.dart';
import '../../../../shared/providers/providers.dart';
import '../widgets/login_form.dart';
import '../widgets/auth_header.dart';
import '../widgets/auth_footer.dart';

/// Login page with responsive design
class LoginPage extends ConsumerStatefulWidget {
  const LoginPage({super.key});

  @override
  ConsumerState<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends ConsumerState<LoginPage> {
  @override
  void initState() {
    super.initState();
    
    // Listen to auth state changes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.listen<BaseState>(authStateNotifierProvider, (previous, next) {
        if (next is SuccessState) {
          // Navigate to home page on successful login
          _handleLoginSuccess();
        } else if (next is ErrorState) {
          // Show error message
          _showErrorSnackBar(next.failure.message);
        }
      });
    });
  }

  void _handleLoginSuccess() {
    // Clear any previous error states
    ref.read(authStateNotifierProvider.notifier).resetState();
    
    // Navigate to home page
    // TODO: Implement navigation when routing is set up
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Login successful! Welcome to BAPELTAN SIMDOC'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
        action: SnackBarAction(
          label: 'Dismiss',
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isLoading = ref.watch(isAuthLoadingProvider);
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > AppConstants.tabletBreakpoint;
    final isDesktop = screenSize.width > AppConstants.desktopBreakpoint;

    return Scaffold(
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(
              horizontal: isDesktop ? 32.0 : 16.0,
              vertical: 24.0,
            ),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: isDesktop ? 400.0 : double.infinity,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Header with logo and title
                  const AuthHeader(),
                  
                  SizedBox(height: isTablet ? 48.0 : 32.0),
                  
                  // Login form
                  Card(
                    elevation: isDesktop ? 8.0 : 4.0,
                    child: Padding(
                      padding: EdgeInsets.all(isTablet ? 32.0 : 24.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Text(
                            'Masuk ke Akun Anda',
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          
                          const SizedBox(height: 8.0),
                          
                          Text(
                            'Silakan masuk untuk mengakses sistem manajemen dokumen',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          
                          SizedBox(height: isTablet ? 32.0 : 24.0),
                          
                          // Login form
                          const LoginForm(),
                        ],
                      ),
                    ),
                  ),
                  
                  SizedBox(height: isTablet ? 32.0 : 24.0),
                  
                  // Footer with additional links
                  const AuthFooter(),
                ],
              ),
            ),
          ),
        ),
      ),
      
      // Loading overlay
      if (isLoading)
        Container(
          color: Colors.black.withOpacity(0.3),
          child: const Center(
            child: Card(
              child: Padding(
                padding: EdgeInsets.all(24.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16.0),
                    Text('Sedang memproses...'),
                  ],
                ),
              ),
            ),
          ),
        ),
    );
  }
}

/// Extension for responsive breakpoints
extension ResponsiveExtension on BuildContext {
  bool get isMobile => MediaQuery.of(this).size.width < AppConstants.tabletBreakpoint;
  bool get isTablet => MediaQuery.of(this).size.width >= AppConstants.tabletBreakpoint && 
                      MediaQuery.of(this).size.width < AppConstants.desktopBreakpoint;
  bool get isDesktop => MediaQuery.of(this).size.width >= AppConstants.desktopBreakpoint;
}
