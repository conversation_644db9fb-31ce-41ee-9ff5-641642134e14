import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'core/constants/app_constants.dart';
import 'core/themes/app_theme.dart';
import 'shared/providers/providers.dart';
import 'features/auth/presentation/pages/splash_page.dart';
import 'features/auth/presentation/pages/login_page.dart';

void main() {
  runApp(const ProviderScope(child: BapeltanSimdocApp()));
}

class BapeltanSimdocApp extends ConsumerWidget {
  const BapeltanSimdocApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentTheme = ref.watch(currentThemeProvider);

    return MaterialApp(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,

      // Theme configuration
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: _getThemeMode(currentTheme),

      // Initial route
      home: const AppInitializer(),

      // Routes
      routes: {
        '/splash': (context) => const SplashPage(),
        '/login': (context) => const LoginPage(),
      },
    );
  }

  ThemeMode _getThemeMode(String theme) {
    switch (theme) {
      case 'dark':
        return ThemeMode.dark;
      case 'light':
        return ThemeMode.light;
      case 'system':
        return ThemeMode.system;
      default:
        return ThemeMode.light;
    }
  }
}

/// App initializer that determines which screen to show
class AppInitializer extends ConsumerWidget {
  const AppInitializer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAppInitialized = ref.watch(isAppInitializedProvider);
    final isAppReady = ref.watch(appReadyProvider);
    final hasInitError = ref.watch(hasInitializationErrorProvider);

    // Show splash screen during initialization
    if (!isAppInitialized || !isAppReady || hasInitError) {
      return const SplashPage();
    }

    // Check authentication state
    final authState = ref.watch(authStateProvider);

    return authState.when(
      data: (user) {
        if (user != null) {
          // User is signed in, show home page
          // TODO: Create and navigate to home page
          return const Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.home, size: 64.0),
                  SizedBox(height: 16.0),
                  Text('Welcome to BAPELTAN SIMDOC!'),
                  Text('Home page will be implemented next.'),
                ],
              ),
            ),
          );
        } else {
          // User is not signed in, show login page
          return const LoginPage();
        }
      },
      loading: () => const SplashPage(),
      error: (error, stackTrace) => const LoginPage(),
    );
  }
}
